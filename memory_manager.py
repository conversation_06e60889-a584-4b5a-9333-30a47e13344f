"""
Memory management system for Matrix-F to prevent crashes and optimize performance.
Provides memory monitoring, garbage collection, and adaptive processing.
"""

import gc
import psutil
import threading
import time
import warnings
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import numpy as np
import pandas as pd


@dataclass
class MemoryStats:
    """Memory statistics for monitoring."""
    total_gb: float
    available_gb: float
    used_gb: float
    usage_percent: float
    swap_used_gb: float
    process_memory_gb: float
    timestamp: datetime


class MemoryManager:
    """
    Comprehensive memory management system.
    """
    
    def __init__(self, 
                 warning_threshold_percent: float = 80.0,
                 critical_threshold_percent: float = 90.0,
                 cleanup_threshold_percent: float = 85.0,
                 monitoring_interval_seconds: float = 5.0):
        """
        Initialize memory manager.
        
        Args:
            warning_threshold_percent: Memory usage % to trigger warnings
            critical_threshold_percent: Memory usage % to trigger aggressive cleanup
            cleanup_threshold_percent: Memory usage % to trigger automatic cleanup
            monitoring_interval_seconds: How often to check memory usage
        """
        self.warning_threshold = warning_threshold_percent
        self.critical_threshold = critical_threshold_percent
        self.cleanup_threshold = cleanup_threshold_percent
        self.monitoring_interval = monitoring_interval_seconds
        
        # Monitoring state
        self.is_monitoring = False
        self.monitor_thread = None
        self.stats_history: List[MemoryStats] = []
        self.max_history_size = 100
        
        # Callbacks
        self.warning_callbacks: List[Callable[[MemoryStats], None]] = []
        self.critical_callbacks: List[Callable[[MemoryStats], None]] = []
        
        # Process reference
        self.process = psutil.Process()
        
        # Memory cleanup registry
        self.cleanup_functions: List[Callable[[], None]] = []
        
        print(f"Memory manager initialized with {self.warning_threshold}% warning threshold")
    
    def get_current_stats(self) -> MemoryStats:
        """Get current memory statistics."""
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()
        
        return MemoryStats(
            total_gb=memory.total / (1024**3),
            available_gb=memory.available / (1024**3),
            used_gb=memory.used / (1024**3),
            usage_percent=memory.percent,
            swap_used_gb=swap.used / (1024**3),
            process_memory_gb=self.process.memory_info().rss / (1024**3),
            timestamp=datetime.now()
        )
    
    def start_monitoring(self):
        """Start background memory monitoring."""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        print("Memory monitoring started")
    
    def stop_monitoring(self):
        """Stop background memory monitoring."""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
        print("Memory monitoring stopped")
    
    def _monitor_loop(self):
        """Background monitoring loop."""
        while self.is_monitoring:
            try:
                stats = self.get_current_stats()
                self._update_history(stats)
                self._check_thresholds(stats)
                time.sleep(self.monitoring_interval)
            except Exception as e:
                print(f"Error in memory monitoring: {e}")
                time.sleep(self.monitoring_interval)
    
    def _update_history(self, stats: MemoryStats):
        """Update statistics history."""
        self.stats_history.append(stats)
        if len(self.stats_history) > self.max_history_size:
            self.stats_history.pop(0)
    
    def _check_thresholds(self, stats: MemoryStats):
        """Check memory thresholds and trigger actions."""
        if stats.usage_percent >= self.critical_threshold:
            print(f"CRITICAL: Memory usage at {stats.usage_percent:.1f}%")
            self._trigger_critical_cleanup(stats)
            for callback in self.critical_callbacks:
                try:
                    callback(stats)
                except Exception as e:
                    print(f"Error in critical callback: {e}")
        
        elif stats.usage_percent >= self.cleanup_threshold:
            print(f"HIGH: Memory usage at {stats.usage_percent:.1f}%, triggering cleanup")
            self._trigger_automatic_cleanup()
        
        elif stats.usage_percent >= self.warning_threshold:
            print(f"WARNING: Memory usage at {stats.usage_percent:.1f}%")
            for callback in self.warning_callbacks:
                try:
                    callback(stats)
                except Exception as e:
                    print(f"Error in warning callback: {e}")
    
    def _trigger_automatic_cleanup(self):
        """Trigger automatic memory cleanup."""
        print("Performing automatic memory cleanup...")
        
        # Run registered cleanup functions
        for cleanup_func in self.cleanup_functions:
            try:
                cleanup_func()
            except Exception as e:
                print(f"Error in cleanup function: {e}")
        
        # Force garbage collection
        self.force_garbage_collection()
    
    def _trigger_critical_cleanup(self, stats: MemoryStats):
        """Trigger aggressive memory cleanup for critical situations."""
        print("Performing CRITICAL memory cleanup...")
        
        # Run all cleanup functions
        self._trigger_automatic_cleanup()
        
        # Additional aggressive cleanup
        self.clear_pandas_caches()
        self.clear_numpy_caches()
        
        # Multiple garbage collection passes
        for i in range(3):
            collected = gc.collect()
            print(f"GC pass {i+1}: collected {collected} objects")
    
    def force_garbage_collection(self) -> int:
        """Force garbage collection and return number of collected objects."""
        collected = gc.collect()
        print(f"Garbage collection: freed {collected} objects")
        return collected
    
    def clear_pandas_caches(self):
        """Clear pandas internal caches."""
        try:
            # Clear pandas caches if available
            if hasattr(pd, '_cache'):
                pd._cache.clear()
            print("Pandas caches cleared")
        except Exception as e:
            print(f"Error clearing pandas caches: {e}")
    
    def clear_numpy_caches(self):
        """Clear numpy internal caches."""
        try:
            # Clear numpy caches if available
            if hasattr(np, '_NoValue'):
                # Clear numpy's internal caches
                pass
            print("Numpy caches cleared")
        except Exception as e:
            print(f"Error clearing numpy caches: {e}")
    
    def register_cleanup_function(self, func: Callable[[], None]):
        """Register a function to be called during memory cleanup."""
        self.cleanup_functions.append(func)
        print(f"Registered cleanup function: {func.__name__}")
    
    def register_warning_callback(self, callback: Callable[[MemoryStats], None]):
        """Register callback for memory warnings."""
        self.warning_callbacks.append(callback)
    
    def register_critical_callback(self, callback: Callable[[MemoryStats], None]):
        """Register callback for critical memory situations."""
        self.critical_callbacks.append(callback)
    
    def get_memory_pressure(self) -> str:
        """Get current memory pressure level."""
        stats = self.get_current_stats()
        
        if stats.usage_percent >= self.critical_threshold:
            return "CRITICAL"
        elif stats.usage_percent >= self.cleanup_threshold:
            return "HIGH"
        elif stats.usage_percent >= self.warning_threshold:
            return "MODERATE"
        else:
            return "LOW"
    
    def get_recommended_batch_size(self, base_size: int, max_size: int = 1000) -> int:
        """Get recommended batch size based on memory pressure."""
        pressure = self.get_memory_pressure()
        stats = self.get_current_stats()
        
        if pressure == "CRITICAL":
            return max(1, base_size // 8)
        elif pressure == "HIGH":
            return max(1, base_size // 4)
        elif pressure == "MODERATE":
            return max(1, base_size // 2)
        else:
            # Scale based on available memory
            memory_factor = min(1.0, stats.available_gb / 4.0)  # Assume 4GB is comfortable
            return min(max_size, max(base_size, int(base_size * memory_factor)))
    
    def check_memory_for_operation(self, estimated_memory_gb: float) -> bool:
        """Check if there's enough memory for an operation."""
        stats = self.get_current_stats()
        
        if stats.available_gb < estimated_memory_gb:
            print(f"Insufficient memory: need {estimated_memory_gb:.1f}GB, have {stats.available_gb:.1f}GB")
            return False
        
        # Also check if operation would push us over threshold
        projected_usage = ((stats.used_gb + estimated_memory_gb) / stats.total_gb) * 100
        
        if projected_usage > self.cleanup_threshold:
            print(f"Operation would push memory usage to {projected_usage:.1f}%")
            return False
        
        return True
    
    def get_stats_summary(self) -> Dict[str, Any]:
        """Get summary of memory statistics."""
        stats = self.get_current_stats()
        
        return {
            'current_usage_percent': stats.usage_percent,
            'available_gb': stats.available_gb,
            'process_memory_gb': stats.process_memory_gb,
            'memory_pressure': self.get_memory_pressure(),
            'monitoring_active': self.is_monitoring,
            'cleanup_functions_registered': len(self.cleanup_functions),
            'history_size': len(self.stats_history)
        }


# Global memory manager instance
_global_memory_manager: Optional[MemoryManager] = None


def get_memory_manager() -> MemoryManager:
    """Get the global memory manager instance."""
    global _global_memory_manager
    if _global_memory_manager is None:
        # Determine thresholds based on total system memory
        memory = psutil.virtual_memory()
        total_gb = memory.total / (1024**3)
        
        # More conservative thresholds for systems with less memory
        if total_gb < 8:
            warning_threshold = 70.0
            critical_threshold = 85.0
            cleanup_threshold = 80.0
        elif total_gb < 16:
            warning_threshold = 75.0
            critical_threshold = 90.0
            cleanup_threshold = 85.0
        else:
            warning_threshold = 80.0
            critical_threshold = 95.0
            cleanup_threshold = 90.0
        
        _global_memory_manager = MemoryManager(
            warning_threshold_percent=warning_threshold,
            critical_threshold_percent=critical_threshold,
            cleanup_threshold_percent=cleanup_threshold,
            monitoring_interval_seconds=3.0
        )
        
        print(f"Initialized memory manager for {total_gb:.1f}GB system")
    
    return _global_memory_manager


def cleanup_dataframes():
    """Cleanup function for large DataFrames."""
    # This would be called during memory pressure
    gc.collect()
    print("DataFrame cleanup completed")


def cleanup_plotly_figures():
    """Cleanup function for Plotly figures."""
    # This would be called during memory pressure
    gc.collect()
    print("Plotly figure cleanup completed")


# Auto-register common cleanup functions
def initialize_memory_management():
    """Initialize memory management with common cleanup functions."""
    manager = get_memory_manager()
    manager.register_cleanup_function(cleanup_dataframes)
    manager.register_cleanup_function(cleanup_plotly_figures)
    manager.start_monitoring()
    return manager
