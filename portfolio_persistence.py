"""
Portfolio persistence module for saving and loading user portfolio data.
"""
import sqlite3
import os
from datetime import datetime
from typing import Optional

class PortfolioPersistence:
    """Handles persistent storage of portfolio data"""
    
    def __init__(self, db_path: str = "portfolio_data.db"):
        """Initialize portfolio persistence with database path"""
        self.db_path = db_path
        self._init_db()
    
    def _init_db(self):
        """Initialize SQLite database for portfolio persistence"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS portfolio_data (
                        id INTEGER PRIMARY KEY,
                        key TEXT UNIQUE,
                        content TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                conn.execute('CREATE INDEX IF NOT EXISTS idx_key ON portfolio_data(key)')
        except Exception as e:
            print(f"Warning: Could not initialize portfolio database: {e}")
    
    def save_portfolio_text(self, key: str, content: str) -> bool:
        """
        Save portfolio text content to database
        
        Args:
            key: Unique identifier for the portfolio data
            content: Text content to save
            
        Returns:
            bool: True if saved successfully
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    INSERT OR REPLACE INTO portfolio_data (key, content, updated_at)
                    VALUES (?, ?, ?)
                ''', (key, content, datetime.now().isoformat()))
            return True
        except Exception as e:
            print(f"Error saving portfolio data: {e}")
            return False
    
    def load_portfolio_text(self, key: str) -> Optional[str]:
        """
        Load portfolio text content from database
        
        Args:
            key: Unique identifier for the portfolio data
            
        Returns:
            str: Saved content or None if not found
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    'SELECT content FROM portfolio_data WHERE key = ?',
                    (key,)
                )
                result = cursor.fetchone()
                return result[0] if result else None
        except Exception as e:
            print(f"Error loading portfolio data: {e}")
            return None

# Global instance
_portfolio_persistence = None

def get_portfolio_persistence() -> PortfolioPersistence:
    """Get the global portfolio persistence instance"""
    global _portfolio_persistence
    if _portfolio_persistence is None:
        _portfolio_persistence = PortfolioPersistence()
    return _portfolio_persistence
