"""
Smart caching system for Matrix-F with memory management and LRU eviction.
Provides intelligent caching with size limits, persistence, and automatic cleanup.
"""

import pickle
import sqlite3
import time
import threading
import psutil
import os
from datetime import datetime, timed<PERSON>ta
from typing import Any, Optional, Dict, Tuple
from collections import OrderedDict
import pytz

# Configure timezone
TRADING_TIMEZONE = pytz.timezone('Europe/Bucharest')

class SmartCache:
    """
    Memory-aware cache with LRU eviction and optional persistence.
    """
    
    def __init__(self, 
                 max_memory_mb: int = 500,
                 max_items: int = 1000,
                 enable_persistence: bool = True,
                 db_path: str = "cache.db"):
        """
        Initialize the smart cache.
        
        Args:
            max_memory_mb: Maximum memory usage in MB
            max_items: Maximum number of cached items
            enable_persistence: Whether to persist cache to disk
            db_path: Path to SQLite database for persistence
        """
        self.max_memory = max_memory_mb * 1024 * 1024  # Convert to bytes
        self.max_items = max_items
        self.enable_persistence = enable_persistence
        self.db_path = db_path
        
        # In-memory cache with LRU ordering
        self._cache: OrderedDict[str, Dict] = OrderedDict()
        self._lock = threading.RLock()
        
        # Initialize persistence if enabled
        if self.enable_persistence:
            self._init_db()
            self._load_from_db()
    
    def _init_db(self):
        """Initialize SQLite database for persistence."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS cache_data (
                        key TEXT PRIMARY KEY,
                        data BLOB,
                        timestamp REAL,
                        expires REAL,
                        size_bytes INTEGER,
                        access_count INTEGER DEFAULT 0,
                        last_access REAL
                    )
                ''')
                conn.execute('CREATE INDEX IF NOT EXISTS idx_expires ON cache_data(expires)')
                conn.execute('CREATE INDEX IF NOT EXISTS idx_last_access ON cache_data(last_access)')
        except Exception as e:
            print(f"Warning: Could not initialize cache database: {e}")
            self.enable_persistence = False
    
    def _load_from_db(self):
        """Load valid cache entries from database."""
        if not self.enable_persistence:
            return
            
        try:
            current_time = time.time()
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute('''
                    SELECT key, data, timestamp, expires, size_bytes, access_count, last_access
                    FROM cache_data 
                    WHERE expires > ? OR expires IS NULL
                    ORDER BY last_access DESC
                    LIMIT ?
                ''', (current_time, self.max_items))
                
                for row in cursor:
                    key, data_blob, timestamp, expires, size_bytes, access_count, last_access = row
                    try:
                        data = pickle.loads(data_blob)
                        self._cache[key] = {
                            'data': data,
                            'timestamp': timestamp,
                            'expires': expires,
                            'size_bytes': size_bytes,
                            'access_count': access_count,
                            'last_access': last_access
                        }
                    except Exception as e:
                        print(f"Warning: Could not load cached item {key}: {e}")
                        
            print(f"Loaded {len(self._cache)} items from cache database")
            
        except Exception as e:
            print(f"Warning: Could not load from cache database: {e}")
    
    def _get_memory_usage(self) -> int:
        """Calculate current memory usage of cache."""
        return sum(item.get('size_bytes', 0) for item in self._cache.values())
    
    def _calculate_size(self, data: Any) -> int:
        """Calculate size of data in bytes."""
        try:
            return len(pickle.dumps(data))
        except Exception:
            return 1024  # Default estimate
    
    def _evict_if_needed(self, new_item_size: int = 0):
        """Evict items if memory or count limits would be exceeded."""
        current_memory = self._get_memory_usage()
        
        # Evict based on memory limit
        while (current_memory + new_item_size > self.max_memory and self._cache):
            # Remove least recently used item
            key, item = self._cache.popitem(last=False)
            current_memory -= item.get('size_bytes', 0)
            print(f"Evicted cache item {key} due to memory limit")
        
        # Evict based on item count limit
        while len(self._cache) >= self.max_items and self._cache:
            key, item = self._cache.popitem(last=False)
            print(f"Evicted cache item {key} due to count limit")
    
    def put(self, key: str, data: Any, expires_in_hours: Optional[float] = None) -> bool:
        """
        Store data in cache.
        
        Args:
            key: Cache key
            data: Data to cache
            expires_in_hours: Expiration time in hours (None for no expiration)
            
        Returns:
            True if successfully cached, False otherwise
        """
        with self._lock:
            try:
                # Calculate data size
                size_bytes = self._calculate_size(data)
                
                # Check if item is too large for cache
                if size_bytes > self.max_memory:
                    print(f"Warning: Item {key} too large for cache ({size_bytes} bytes)")
                    return False
                
                # Evict items if needed
                self._evict_if_needed(size_bytes)
                
                # Prepare cache item
                current_time = time.time()
                expires = current_time + (expires_in_hours * 3600) if expires_in_hours else None
                
                cache_item = {
                    'data': data,
                    'timestamp': current_time,
                    'expires': expires,
                    'size_bytes': size_bytes,
                    'access_count': 0,
                    'last_access': current_time
                }
                
                # Store in memory cache
                self._cache[key] = cache_item
                self._cache.move_to_end(key)  # Mark as most recently used
                
                # Persist to database if enabled
                if self.enable_persistence:
                    self._persist_item(key, cache_item)
                
                print(f"Cached item {key} ({size_bytes} bytes)")
                return True
                
            except Exception as e:
                print(f"Error caching item {key}: {e}")
                return False
    
    def get(self, key: str) -> Optional[Any]:
        """
        Retrieve data from cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cached data if found and valid, None otherwise
        """
        with self._lock:
            if key not in self._cache:
                return None
            
            cache_item = self._cache[key]
            current_time = time.time()
            
            # Check expiration
            if cache_item.get('expires') and current_time > cache_item['expires']:
                del self._cache[key]
                if self.enable_persistence:
                    self._remove_from_db(key)
                print(f"Cache item {key} expired")
                return None
            
            # Update access statistics
            cache_item['access_count'] += 1
            cache_item['last_access'] = current_time
            
            # Move to end (most recently used)
            self._cache.move_to_end(key)
            
            # Update database if enabled
            if self.enable_persistence:
                self._update_access_stats(key, cache_item)
            
            return cache_item['data']
    
    def _persist_item(self, key: str, cache_item: Dict):
        """Persist cache item to database."""
        try:
            data_blob = pickle.dumps(cache_item['data'])
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    INSERT OR REPLACE INTO cache_data 
                    (key, data, timestamp, expires, size_bytes, access_count, last_access)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    key, data_blob, cache_item['timestamp'], cache_item['expires'],
                    cache_item['size_bytes'], cache_item['access_count'], cache_item['last_access']
                ))
        except Exception as e:
            print(f"Warning: Could not persist cache item {key}: {e}")
    
    def _update_access_stats(self, key: str, cache_item: Dict):
        """Update access statistics in database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    UPDATE cache_data 
                    SET access_count = ?, last_access = ?
                    WHERE key = ?
                ''', (cache_item['access_count'], cache_item['last_access'], key))
        except Exception as e:
            print(f"Warning: Could not update access stats for {key}: {e}")
    
    def _remove_from_db(self, key: str):
        """Remove item from database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('DELETE FROM cache_data WHERE key = ?', (key,))
        except Exception as e:
            print(f"Warning: Could not remove {key} from database: {e}")
    
    def clear(self):
        """Clear all cache data."""
        with self._lock:
            self._cache.clear()
            if self.enable_persistence:
                try:
                    with sqlite3.connect(self.db_path) as conn:
                        conn.execute('DELETE FROM cache_data')
                    print("Cache database cleared")
                except Exception as e:
                    print(f"Warning: Could not clear cache database: {e}")
    
    def cleanup_expired(self):
        """Remove expired items from cache and database."""
        with self._lock:
            current_time = time.time()
            expired_keys = []
            
            for key, item in self._cache.items():
                if item.get('expires') and current_time > item['expires']:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self._cache[key]
            
            if self.enable_persistence and expired_keys:
                try:
                    with sqlite3.connect(self.db_path) as conn:
                        conn.execute('DELETE FROM cache_data WHERE expires <= ?', (current_time,))
                    print(f"Cleaned up {len(expired_keys)} expired cache items")
                except Exception as e:
                    print(f"Warning: Could not cleanup expired items: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._lock:
            memory_usage = self._get_memory_usage()
            return {
                'items': len(self._cache),
                'memory_usage_mb': memory_usage / (1024 * 1024),
                'memory_limit_mb': self.max_memory / (1024 * 1024),
                'memory_usage_percent': (memory_usage / self.max_memory) * 100,
                'item_limit': self.max_items,
                'persistence_enabled': self.enable_persistence
            }

# Global cache instance
_global_cache = None

def get_cache() -> SmartCache:
    """Get the global cache instance."""
    global _global_cache
    if _global_cache is None:
        # Determine cache size based on available memory
        memory = psutil.virtual_memory()
        available_gb = memory.available / (1024**3)
        
        # Use up to 10% of available memory for cache, with reasonable limits
        cache_mb = min(max(100, int(available_gb * 1024 * 0.1)), 2000)
        
        _global_cache = SmartCache(
            max_memory_mb=cache_mb,
            max_items=5000,
            enable_persistence=True,
            db_path="mpt_cache.db"
        )
        print(f"Initialized smart cache with {cache_mb}MB limit")
    
    return _global_cache
