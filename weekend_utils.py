"""
Weekend detection and handling utilities for Matrix-F.
Provides functions to detect weekends and manage data freezing during non-trading hours.
"""

from datetime import datetime, timedelta
import pytz
import dash

# Configure timezone - should match your trading timezone
TRADING_TIMEZONE = pytz.timezone('Europe/Bucharest')

def is_weekend():
    """
    Check if current time is during weekend (Saturday or Sunday).

    Returns:
        bool: True if it's weekend, False otherwise
    """
    now = datetime.now(TRADING_TIMEZONE)
    weekday = now.weekday()
    is_weekend_result = weekday >= 5  # 5 = Saturday, 6 = Sunday
    print(f"Weekend check: {now.strftime('%Y-%m-%d %H:%M:%S %Z')}, weekday={weekday}, is_weekend={is_weekend_result}")
    return is_weekend_result

def get_last_friday_end():
    """
    Get the end time of the last Friday (23:59:59).
    
    Returns:
        datetime: Last Friday's end time in trading timezone
    """
    now = datetime.now(TRADING_TIMEZONE)
    
    # Calculate days back to Friday
    if now.weekday() == 5:  # Saturday
        days_back = 1
    elif now.weekday() == 6:  # Sunday
        days_back = 2
    else:  # Weekday
        if now.weekday() < 4:  # Monday to Thursday
            days_back = (now.weekday() + 3) % 7  # Go back to previous Friday
        else:  # Friday
            days_back = 0  # Current day is Friday
    
    last_friday = now - timedelta(days=days_back)
    last_friday_end = last_friday.replace(hour=23, minute=59, second=59, microsecond=0)
    
    return last_friday_end

def should_freeze_updates():
    """
    Determine if updates should be frozen (during weekends).

    Returns:
        bool: True if updates should be frozen, False otherwise
    """
    return is_weekend()

def should_use_friday_data():
    """
    Determine if we should try to use Friday data during weekends.
    This is more lenient than should_freeze_updates - it allows fallback to live data.

    Returns:
        bool: True if we should prefer Friday data, False otherwise
    """
    return is_weekend()

def prevent_weekend_update():
    """
    Dash callback decorator to prevent updates during weekends.
    Use this to automatically prevent callback execution during weekends.
    
    Usage:
        @prevent_weekend_update()
        def my_callback(...):
            # This will not execute during weekends
            pass
    """
    if should_freeze_updates():
        raise dash.exceptions.PreventUpdate

def get_weekend_status_message():
    """
    Get a status message indicating weekend mode.
    
    Returns:
        str: Status message for weekend mode
    """
    if is_weekend():
        last_friday = get_last_friday_end()
        return f"Weekend Mode: Showing Friday's data (last update: {last_friday.strftime('%Y-%m-%d %H:%M:%S')})"
    else:
        return "Live Trading Mode: Real-time updates active"

def log_weekend_status():
    """
    Log the current weekend status for debugging.
    """
    now = datetime.now(TRADING_TIMEZONE)
    status = "WEEKEND" if is_weekend() else "TRADING"
    print(f"[{now.strftime('%Y-%m-%d %H:%M:%S')}] Weekend Status: {status}")
    
    if is_weekend():
        last_friday = get_last_friday_end()
        print(f"[{now.strftime('%Y-%m-%d %H:%M:%S')}] Last Friday end: {last_friday.strftime('%Y-%m-%d %H:%M:%S')}")

# Import smart cache system
try:
    from smart_cache import get_cache
    _use_smart_cache = True
    print("Smart cache system loaded successfully")
except ImportError as e:
    print(f"Warning: Could not load smart cache system: {e}")
    _use_smart_cache = False

# Initialize fallback cache regardless of smart cache availability
_friday_data_cache = {}

def cache_friday_data(key, data):
    """
    Cache data for weekend use with smart caching system.

    Args:
        key (str): Cache key identifier
        data: Data to cache
    """
    global _friday_data_cache

    weekend_check = is_weekend()
    if not weekend_check:  # Only cache during trading days
        if _use_smart_cache:
            try:
                cache = get_cache()
                # Cache for 72 hours (weekend duration)
                success = cache.put(f"friday_{key}", {
                    'data': data,
                    'timestamp': datetime.now(TRADING_TIMEZONE),
                    'friday_end': get_last_friday_end()
                }, expires_in_hours=72)

                if success:
                    print(f"Data cached with smart cache for key: {key}")
                else:
                    print(f"Failed to cache data with smart cache for key: {key}")
            except Exception as e:
                print(f"Error using smart cache, falling back to simple cache: {e}")
                _friday_data_cache[key] = {
                    'data': data,
                    'timestamp': datetime.now(TRADING_TIMEZONE),
                    'friday_end': get_last_friday_end()
                }
                print(f"Data cached with fallback cache for key: {key}")
        else:
            # Fallback to simple cache
            _friday_data_cache[key] = {
                'data': data,
                'timestamp': datetime.now(TRADING_TIMEZONE),
                'friday_end': get_last_friday_end()
            }
            print(f"Data cached with simple cache for key: {key}")
    else:
        print(f"Not caching data during weekend for key: {key}")

def get_cached_friday_data(key):
    """
    Get cached Friday data during weekends with smart caching system.

    Args:
        key (str): Cache key identifier

    Returns:
        Cached data if available and valid, None otherwise
    """
    global _friday_data_cache

    weekend_check = is_weekend()
    if not weekend_check:
        print(f"Not using cache during trading days for key: {key}")
        return None  # Don't use cache during trading days

    if _use_smart_cache:
        try:
            cache = get_cache()
            cached_item = cache.get(f"friday_{key}")

            if cached_item is None:
                print(f"No cached data found in smart cache for key: {key}")
                return None

            # Check if cached data is from the correct Friday
            current_friday_end = get_last_friday_end()
            if cached_item['friday_end'].date() != current_friday_end.date():
                print(f"Cached data is from wrong Friday, ignoring key: {key}")
                return None

            print(f"Using cached Friday data from smart cache for key: {key}")
            return cached_item['data']

        except Exception as e:
            print(f"Error using smart cache, falling back to simple cache: {e}")
            # Fall through to simple cache

    # Fallback to simple cache
    if key not in _friday_data_cache:
        print(f"No cached data found in simple cache for key: {key}")
        return None

    cached_item = _friday_data_cache[key]

    # Check if cached data is from the correct Friday
    current_friday_end = get_last_friday_end()
    if cached_item['friday_end'].date() != current_friday_end.date():
        # Cached data is from a different Friday, invalidate
        print(f"Cached data is from wrong Friday, invalidating key: {key}")
        del _friday_data_cache[key]
        return None

    print(f"Using cached Friday data from simple cache for key: {key}")
    return cached_item['data']

def clear_weekend_cache():
    """
    Clear the weekend data cache.
    Call this at the start of a new trading week.
    """
    global _friday_data_cache

    if _use_smart_cache:
        try:
            cache = get_cache()
            # Clean up expired items instead of clearing everything
            cache.cleanup_expired()
            print(f"[{datetime.now(TRADING_TIMEZONE).strftime('%Y-%m-%d %H:%M:%S')}] Smart cache cleaned up")
        except Exception as e:
            print(f"Error cleaning smart cache: {e}")

    # Also clear simple cache
    _friday_data_cache.clear()
    print(f"[{datetime.now(TRADING_TIMEZONE).strftime('%Y-%m-%d %H:%M:%S')}] Simple cache cleared")

def is_new_trading_week():
    """
    Check if this is the start of a new trading week (Monday).
    
    Returns:
        bool: True if it's Monday, False otherwise
    """
    now = datetime.now(TRADING_TIMEZONE)
    return now.weekday() == 0  # 0 = Monday

# Auto-clear cache on new trading week
if is_new_trading_week() and not is_weekend():
    clear_weekend_cache()

def get_cache_stats():
    """
    Get cache statistics for monitoring and debugging.

    Returns:
        dict: Cache statistics including memory usage and item counts
    """
    stats = {
        'smart_cache_enabled': _use_smart_cache,
        'simple_cache_items': len(_friday_data_cache) if '_friday_data_cache' in globals() else 0
    }

    if _use_smart_cache:
        try:
            cache = get_cache()
            smart_stats = cache.get_stats()
            stats.update({
                'smart_cache_stats': smart_stats,
                'total_memory_usage_mb': smart_stats.get('memory_usage_mb', 0)
            })
        except Exception as e:
            stats['smart_cache_error'] = str(e)

    return stats
