"""
MT5 Trading Module for MPT Application
Handles sending portfolio trades directly to MetaTrader 5 based on MPT allocations
"""

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import time

# Configure logging
logger = logging.getLogger(__name__)


class MPTTrader:
    """Handles trade execution for MPT portfolio allocations"""

    def __init__(self):
        """Initialize MPT trader"""
        self.connected = False

    def connect(self) -> bool:
        """
        Connect to MT5 if not already connected

        Returns:
            bool: True if connected successfully
        """
        try:
            if mt5.initialize(path=r"E:\\AMP MetaTrader 5\\terminal64.exe", portable=True):
                self.connected = True
                logger.info("MPTTrader connected successfully")
                return True
            else:
                logger.error(f"MPTTrader connection failed: {mt5.last_error()}")
                return False
        except Exception as e:
            logger.error(f"MPTTrader connection exception: {str(e)}")
            return False

    def disconnect(self):
        """Disconnect from MT5"""
        mt5.shutdown()
        self.connected = False

    def get_account_info(self) -> Optional[Dict]:
        """
        Get MT5 account information

        Returns:
            Dict with account info or None if failed
        """
        try:
            if not self.connected and not self.connect():
                return None

            account_info = mt5.account_info()
            if account_info is None:
                return None

            return {
                'login': account_info.login,
                'server': account_info.server,
                'currency': account_info.currency,
                'balance': account_info.balance,
                'equity': account_info.equity,
                'profit': account_info.profit,
                'margin': account_info.margin,
                'free_margin': account_info.margin_free,
                'margin_level': account_info.margin_level
            }

        except Exception as e:
            logger.error(f"Error getting account info: {str(e)}")
            return None

    def send_mpt_portfolio_to_mt5(self,
                                 portfolio_weights: Dict,
                                 total_lot_size: float = 1.0,
                                 close_existing: bool = True,
                                 strategy_name: str = "MPT") -> Dict:
        """
        Send MPT portfolio trades to MT5

        Args:
            portfolio_weights: Dictionary with symbol:weight pairs from MPT
            total_lot_size: Total lot size to distribute across portfolio
            close_existing: Whether to close existing positions first
            strategy_name: Strategy name to use in trade comments

        Returns:
            Dict with execution results
        """
        try:
            if not self.connected and not self.connect():
                return {
                    'success': False,
                    'error': 'Failed to connect to MT5',
                    'trades': []
                }

            if not portfolio_weights:
                return {
                    'success': False,
                    'error': 'No portfolio weights provided',
                    'trades': []
                }

            logger.info(f"Sending MPT portfolio to MT5 | {len(portfolio_weights)} pairs | Total lot size: {total_lot_size}")

            results = {
                'success': True,
                'strategy': strategy_name,
                'trades': [],
                'errors': []
            }

            # Close existing positions if requested
            if close_existing:
                close_results = self._close_all_positions()
                if close_results['errors']:
                    results['errors'].extend(close_results['errors'])

            # Execute new trades based on MPT weights
            for symbol, weight in portfolio_weights.items():
                if abs(weight) < 0.001:  # Skip very small weights
                    continue
                    
                trade_result = self._execute_mpt_trade(symbol, weight, total_lot_size, strategy_name)
                results['trades'].append(trade_result)

                if not trade_result['success']:
                    error_msg = f"Failed to trade {symbol}: {trade_result['error']}"
                    results['errors'].append(error_msg)
                    logger.error(error_msg)
                else:
                    logger.info(f"Successfully executed trade: {trade_result['comment']}")

            # Check overall success
            successful_trades = sum(1 for trade in results['trades'] if trade['success'])
            results['success'] = successful_trades > 0
            results['summary'] = f"Executed {successful_trades}/{len(results['trades'])} trades successfully"

            logger.info(f"MPT Portfolio execution complete: {results['summary']}")
            if results['errors']:
                logger.error(f"Trade errors: {results['errors']}")
            return results

        except Exception as e:
            logger.error(f"MPT Portfolio execution failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'trades': []
            }

    def _execute_mpt_trade(self, symbol: str, weight: float, total_lot_size: float, strategy_name: str = "MPT") -> Dict:
        """
        Execute a single trade based on MPT weight

        Args:
            symbol: Currency pair symbol
            weight: Portfolio weight (positive for buy, negative for sell)
            total_lot_size: Total lot size to distribute
            strategy_name: Strategy name to use in trade comment

        Returns:
            Dict with trade execution result
        """
        try:
            # Calculate position size based on weight
            position_size = abs(weight) * total_lot_size

            # Determine trade direction
            trade_type = mt5.ORDER_TYPE_BUY if weight > 0 else mt5.ORDER_TYPE_SELL
            action_str = "BUY" if weight > 0 else "SELL"

            logger.info(f"Attempting to execute {action_str} {position_size:.2f} lots of {symbol} | Weight: {weight:.4f}")

            # Get symbol info
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                error_msg = f'Symbol {symbol} not found'
                logger.error(error_msg)
                return {
                    'success': False,
                    'symbol': symbol,
                    'error': error_msg
                }

            if not symbol_info.visible:
                # Try to enable symbol
                if not mt5.symbol_select(symbol, True):
                    return {
                        'success': False,
                        'symbol': symbol,
                        'error': f'Failed to enable symbol {symbol}'
                    }

            # Get current tick
            tick = mt5.symbol_info_tick(symbol)
            if tick is None:
                return {
                    'success': False,
                    'symbol': symbol,
                    'error': f'Failed to get tick data for {symbol}'
                }

            price = tick.ask if trade_type == mt5.ORDER_TYPE_BUY else tick.bid

            # Calculate stop loss (50 pips)
            point = symbol_info.point
            sl_distance = 50 * point * 10  # 50 pips converted to price distance

            if trade_type == mt5.ORDER_TYPE_BUY:
                sl_price = price - sl_distance
            else:  # SELL
                sl_price = price + sl_distance

            # Determine comment based on strategy type
            if strategy_name.endswith("_Basket"):
                comment = strategy_name  # Just use strategy name for baskets (e.g., "USD_Basket")
            else:
                comment = f"{strategy_name}_Portfolio"  # Add _Portfolio for non-basket strategies

            # Prepare trade request
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": round(position_size, 2),
                "type": trade_type,
                "price": price,
                "sl": round(sl_price, symbol_info.digits),  # 50 pip stop loss
                "tp": 0.0,  # No take profit
                "deviation": 20,
                "magic": 234567,  # Magic number for MPT identification
                "comment": comment,
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }

            # Send trade request
            logger.info(f"Sending trade request: {request}")
            result = mt5.order_send(request)

            if result.retcode != mt5.TRADE_RETCODE_DONE:
                error_msg = f'Trade failed: {result.comment} (code: {result.retcode})'
                logger.error(f"Trade execution failed for {symbol} | Error: {error_msg}")
                return {
                    'success': False,
                    'symbol': symbol,
                    'weight': weight,
                    'error': error_msg
                }

            return {
                'success': True,
                'symbol': symbol,
                'weight': weight,
                'action': action_str,
                'volume': position_size,
                'price': price,
                'ticket': result.order,
                'comment': f'Executed {action_str} {position_size:.2f} lots of {symbol} at {price:.5f}'
            }

        except Exception as e:
            return {
                'success': False,
                'symbol': symbol,
                'weight': weight,
                'error': str(e)
            }

    def _close_all_positions(self) -> Dict:
        """
        Close all open positions

        Returns:
            Dict with closing results
        """
        try:
            positions = mt5.positions_get()
            if positions is None:
                return {'closed': 0, 'errors': []}

            results = {'closed': 0, 'errors': []}

            for position in positions:
                try:
                    # Prepare close request
                    close_request = {
                        "action": mt5.TRADE_ACTION_DEAL,
                        "symbol": position.symbol,
                        "volume": position.volume,
                        "type": mt5.ORDER_TYPE_SELL if position.type == mt5.ORDER_TYPE_BUY else mt5.ORDER_TYPE_BUY,
                        "position": position.ticket,
                        "deviation": 20,
                        "magic": 234567,
                        "comment": "MPT - Close existing",
                        "type_time": mt5.ORDER_TIME_GTC,
                        "type_filling": mt5.ORDER_FILLING_IOC,
                    }

                    # Get current price for closing
                    tick = mt5.symbol_info_tick(position.symbol)
                    if tick:
                        close_request["price"] = tick.bid if position.type == mt5.ORDER_TYPE_BUY else tick.ask

                    result = mt5.order_send(close_request)

                    if result.retcode == mt5.TRADE_RETCODE_DONE:
                        results['closed'] += 1
                        logger.info(f"Closed position {position.ticket} for {position.symbol}")
                    else:
                        error_msg = f"Failed to close {position.symbol}: {result.comment}"
                        results['errors'].append(error_msg)
                        logger.warning(error_msg)

                except Exception as e:
                    error_msg = f"Error closing position {position.symbol}: {str(e)}"
                    results['errors'].append(error_msg)
                    logger.error(error_msg)

            logger.info(f"Closed {results['closed']} positions with {len(results['errors'])} errors")
            return results

        except Exception as e:
            logger.error(f"Error in close_all_positions: {str(e)}")
            return {'closed': 0, 'errors': [str(e)]}


# Global trader instance
mpt_trader = MPTTrader()
