"""
Dash callbacks for portfolio persistence functionality.
"""
from dash import callback, Output, Input, State
from portfolio_persistence import get_portfolio_persistence

# Portfolio persistence key
PORTFOLIO_KEY = "user_trading_portfolios"

@callback(
    [Output('save-status', 'children'),
     Output('save-status', 'style')],
    [Input('save-portfolios-btn', 'n_clicks')],
    [State('persistent-portfolio-editbox', 'value')]
)
def save_portfolio_data(n_clicks, portfolio_text):
    """Save portfolio text to persistent storage"""
    if n_clicks == 0:
        return "", {'color': 'lightgreen', 'fontSize': '12px'}
    
    if not portfolio_text:
        return "No content to save", {'color': 'orange', 'fontSize': '12px'}
    
    try:
        persistence = get_portfolio_persistence()
        success = persistence.save_portfolio_text(PORTFOLIO_KEY, portfolio_text)
        
        if success:
            return "✓ Portfolios saved successfully", {'color': 'lightgreen', 'fontSize': '12px'}
        else:
            return "✗ Failed to save portfolios", {'color': 'red', 'fontSize': '12px'}
    except Exception as e:
        print(f"Error in save_portfolio_data callback: {e}")
        return "✗ Error saving portfolios", {'color': 'red', 'fontSize': '12px'}

@callback(
    Output('persistent-portfolio-editbox', 'value'),
    [Input('persistent-portfolio-editbox', 'id')]  # Trigger on component load
)
def load_portfolio_data(_):
    """Load portfolio text from persistent storage on app startup"""
    try:
        persistence = get_portfolio_persistence()
        saved_content = persistence.load_portfolio_text(PORTFOLIO_KEY)
        
        if saved_content:
            print("Loaded saved portfolio data from database")
            return saved_content
        else:
            print("No saved portfolio data found")
            return ""
    except Exception as e:
        print(f"Error loading portfolio data: {e}")
        return ""
