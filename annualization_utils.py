import numpy as np
import MetaTrader5 as mt5

def get_annualization_factor(timeframe, data_length=None):
    """
    Calculate annualization factor based on timeframe and data characteristics.
    
    Args:
        timeframe: MT5 timeframe constant (e.g., mt5.TIMEFRAME_M1, mt5.TIMEFRAME_M15)
        data_length: Optional length of data for validation
    
    Returns:
        tuple: (periods_per_year, sqrt_periods_per_year) for returns and volatility scaling
    """
    # Trading days per year (standard assumption)
    TRADING_DAYS_PER_YEAR = 252
    
    # Map MT5 timeframes to periods per day
    timeframe_periods = {
        mt5.TIMEFRAME_M1: 24 * 60,      # 1440 minutes per day
        mt5.TIMEFRAME_M5: 24 * 12,      # 288 periods per day
        mt5.TIMEFRAME_M15: 24 * 4,      # 96 periods per day
        mt5.TIMEFRAME_M30: 24 * 2,      # 48 periods per day
        mt5.TIMEFRAME_H1: 24,           # 24 hours per day
        mt5.TIMEFRAME_H4: 6,            # 6 periods per day
        mt5.TIMEFRAME_D1: 1,            # 1 day per day
    }
    
    periods_per_day = timeframe_periods.get(timeframe, 24 * 4)  # Default to M15
    periods_per_year = TRADING_DAYS_PER_YEAR * periods_per_day
    sqrt_periods_per_year = np.sqrt(periods_per_year)
    
    return periods_per_year, sqrt_periods_per_year

def annualize_portfolio_metrics(metrics, timeframe, annualize=True):
    """
    Annualize portfolio metrics (returns and risk) based on timeframe.
    
    Args:
        metrics: Dictionary containing portfolio metrics
        timeframe: MT5 timeframe constant
        annualize: Boolean flag to enable/disable annualization
    
    Returns:
        Dictionary with potentially annualized metrics
    """
    if not annualize:
        return metrics.copy()
    
    periods_per_year, sqrt_periods_per_year = get_annualization_factor(timeframe)
    
    annualized_metrics = metrics.copy()
    
    # Annualize return (multiply by periods per year)
    if 'return' in metrics:
        annualized_metrics['return'] = metrics['return'] * periods_per_year
    
    # Annualize risk/volatility (multiply by sqrt of periods per year)
    if 'risk' in metrics:
        annualized_metrics['risk'] = metrics['risk'] * sqrt_periods_per_year
    
    # Annualize downside risk
    if 'drisk' in metrics:
        annualized_metrics['drisk'] = metrics['drisk'] * sqrt_periods_per_year
    
    # Ratios remain the same since both numerator and denominator are scaled
    # Sharpe, Sortino, Omega, Calmar ratios are scale-invariant
    
    return annualized_metrics

def annualize_frontier_data(frontier_data, timeframe, annualize=True):
    """
    Annualize efficient frontier data points.
    
    Args:
        frontier_data: List of (risk, return) tuples or similar data structure
        timeframe: MT5 timeframe constant
        annualize: Boolean flag to enable/disable annualization
    
    Returns:
        Annualized frontier data
    """
    if not annualize or not frontier_data:
        return frontier_data
    
    periods_per_year, sqrt_periods_per_year = get_annualization_factor(timeframe)
    
    annualized_data = []
    for risk, ret in frontier_data:
        annualized_risk = risk * sqrt_periods_per_year
        annualized_return = ret * periods_per_year
        annualized_data.append((annualized_risk, annualized_return))
    
    return annualized_data

def annualize_candidate_list(candidates, timeframe, annualize=True):
    """
    Annualize a list of portfolio candidates.
    
    Args:
        candidates: List of portfolio candidate dictionaries
        timeframe: MT5 timeframe constant
        annualize: Boolean flag to enable/disable annualization
    
    Returns:
        List of candidates with potentially annualized metrics
    """
    if not annualize:
        return candidates
    
    annualized_candidates = []
    for candidate in candidates:
        annualized_candidate = annualize_portfolio_metrics(candidate, timeframe, annualize=True)
        annualized_candidates.append(annualized_candidate)
    
    return annualized_candidates

def get_timeframe_description(timeframe, annualize=True):
    """
    Get a human-readable description of the timeframe and scaling.
    
    Args:
        timeframe: MT5 timeframe constant
        annualize: Boolean flag indicating if data is annualized
    
    Returns:
        String description for chart labels
    """
    timeframe_names = {
        mt5.TIMEFRAME_M1: "1-minute",
        mt5.TIMEFRAME_M5: "5-minute", 
        mt5.TIMEFRAME_M15: "15-minute",
        mt5.TIMEFRAME_M30: "30-minute",
        mt5.TIMEFRAME_H1: "1-hour",
        mt5.TIMEFRAME_H4: "4-hour",
        mt5.TIMEFRAME_D1: "daily",
    }
    
    timeframe_name = timeframe_names.get(timeframe, "15-minute")
    
    if annualize:
        return f"Annualized ({timeframe_name} data)"
    else:
        return f"Raw period ({timeframe_name} data)"

def format_return_percentage(return_value, annualize=True):
    """
    Format return value as percentage with appropriate scaling.
    
    Args:
        return_value: Raw return value
        annualize: Boolean indicating if the value is annualized
    
    Returns:
        Formatted string
    """
    if annualize:
        # Annualized returns are typically shown as percentages
        return f"{return_value * 100:.2f}%"
    else:
        # Raw period returns are very small, show more decimal places
        return f"{return_value * 100:.6f}%"

def format_risk_percentage(risk_value, annualize=True):
    """
    Format risk/volatility value as percentage with appropriate scaling.
    
    Args:
        risk_value: Raw risk/volatility value
        annualize: Boolean indicating if the value is annualized
    
    Returns:
        Formatted string
    """
    if annualize:
        # Annualized volatility is typically shown as percentages
        return f"{risk_value * 100:.2f}%"
    else:
        # Raw period volatility is very small, show more decimal places
        return f"{risk_value * 100:.6f}%"
