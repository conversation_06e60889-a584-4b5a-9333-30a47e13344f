import MetaTrader5 as mt5
import numpy as np
import pandas as pd
import psutil
import time
from datetime import datetime, timedelta
import pytz

##################################################
#---RETURNS---#
def calculate_log_returns(data, copy=True):
    """
    Calculate log returns for each symbol in the input data dict.
    Returns a DataFrame of log returns.
    """
    if not isinstance(data, dict):
        raise ValueError("data must be a dictionary of DataFrames")
    log_returns = {}
    for symbol, df in data.items():
        if df.empty or 'close' not in df.columns:
            continue
        if copy:
            dfc = df.copy()
        else:
            dfc = df
        log_ret = np.log(dfc['close'] / dfc['close'].shift(1))
        log_returns[symbol] = log_ret
    return pd.DataFrame(log_returns)

##################################################
#---MT5 & DATA---#
def connect_mt5():
    if not mt5.initialize(path="E:\\icdemomt5\\terminal64.exe", portable=True):
        print("Failed to initialize MT5")
        return False
    return True

def fetch_data(symbols, timeframe=mt5.TIMEFRAME_M1, shift=0, hours=240, start_time=None, end_time=None):
    current_usage = psutil.cpu_percent(interval=0.1)
    if current_usage > 80:
        time.sleep(0.2)
    elif current_usage > 70:
        time.sleep(0.1)

    # Determine start and end times
    if start_time is None or end_time is None:
        # Default behavior: calculate time range automatically
        now_local = datetime.now(pytz.timezone('Europe/Bucharest'))
        print(f"fetch_data: Current time: {now_local.strftime('%Y-%m-%d %H:%M:%S %Z')}, Weekday: {now_local.weekday()}")

        # Smart end time calculation based on weekday
        if now_local.weekday() >= 5:  # Weekend (Saturday=5, Sunday=6)
            # On weekends, get full Friday data (no +3 hours needed)
            days_to_friday = (now_local.weekday() - 4) % 7
            last_friday_date = (now_local - timedelta(days=days_to_friday)).date()
            end_calc = pytz.timezone('Europe/Bucharest').localize(datetime.combine(last_friday_date, datetime.max.time())).replace(hour=23, minute=59, second=59, microsecond=0)
            print(f"Weekend mode: Using Friday end time: {end_calc}")
        else:  # Weekday (Monday=0 to Friday=4)
            # On weekdays, use current time (no +3 hours to avoid future data issues)
            end_calc = now_local
            print(f"Weekday mode: Using current time: {end_calc}")

        start_calc = end_calc - timedelta(hours=hours)
        date_rng = pd.date_range(start_calc, end_calc, freq='min')
        bus_minutes = sum(1 for dt in date_rng if dt.weekday() < 5)
        required_minutes = hours * 60
        if bus_minutes < required_minutes:
            time_diff_estimate = timedelta(hours=(required_minutes - bus_minutes) / 60 * 1.5)
            start_calc = start_calc - time_diff_estimate
            while start_calc.weekday() >= 5:
                start_calc -= timedelta(days=1)
    else:
        # When start_time/end_time are provided explicitly (from mpt_tracker)
        # Use them directly without modification
        start_calc = start_time
        end_calc = end_time
        print(f"fetch_data: Using explicit time range: {start_calc} to {end_calc}")

    data = {}
    for symbol in symbols:
        try:
            # MT5 has a quirk: it expects UTC timestamps but returns local time data
            # To get the correct time range, we need to add the timezone offset to our request
            offset_hours = start_calc.utcoffset().total_seconds() / 3600
            start_adjusted = start_calc + timedelta(hours=offset_hours)
            end_adjusted = end_calc + timedelta(hours=offset_hours)

            start_ts = int(start_adjusted.astimezone(pytz.UTC).timestamp())
            end_ts = int(end_adjusted.astimezone(pytz.UTC).timestamp())
            rates = mt5.copy_rates_range(symbol, timeframe, start_ts, end_ts)
            if rates is None:
                print(f"Failed to retrieve data for {symbol}")
                continue

            df = pd.DataFrame(rates)[['time', 'close']]
            # MT5 returns timestamps that are already in local time (Europe/Bucharest)
            # but labeled as UTC timestamps. We need to treat them as local time directly.
            df['time'] = pd.to_datetime(df['time'], unit='s').dt.tz_localize('Europe/Bucharest')
            df.set_index('time', inplace=True)
            df.sort_index(inplace=True)

            # Filter to only include the requested time range in local time
            if start_time is not None and end_time is not None:
                # When explicit times are provided, filter to exact range
                df = df[(df.index >= start_calc) & (df.index <= end_calc)]
                print(f"Filtered {symbol} to explicit range: {len(df)} points")

            # Remove weekend data
            df = df[df.index.dayofweek < 5]
            df = df[~((df.index.dayofweek == 5) | (df.index.dayofweek == 6))]
            data[symbol] = df
        except Exception as e:
            print(f"Exception fetching data for {symbol}: {e}")
            continue
    return data

##################################################
#---DATA HANDLING---#
def calculate_returns_usd(data):
    returns = {}
    for symbol, df in data.items():
        if df.empty:
            continue
        symbol_info = mt5.symbol_info(symbol)
        multiplier = (symbol_info.trade_tick_value / symbol_info.trade_tick_size) if symbol_info else 1
        df['usd_return'] = np.multiply(np.diff(df['close'], prepend=np.nan), multiplier)
        returns[symbol] = df['usd_return']
    return pd.DataFrame(returns)

def calculate_returns(data, copy=True):
    """
    Calculate log returns for each symbol in the input data dict.
    Uses calculate_log_returns for consistency.
    """
    return calculate_log_returns(data, copy=copy)

def convert_log_to_arithmetic_returns(log_returns_df):
    """
    Convert log returns to arithmetic returns for portfolio optimization.

    Formula: arithmetic_return = exp(log_return) - 1

    This should be used when feeding returns into mean vector μ and covariance matrix Σ
    for portfolio optimization, while keeping log returns for chaining and display.
    """
    return np.exp(log_returns_df) - 1

# Removed duplicate calculate_log_returns definition (was redundant and caused signature mismatch)