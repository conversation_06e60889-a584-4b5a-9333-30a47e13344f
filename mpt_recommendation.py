from app import app
import dash
from dash import Output, Input, State
import json

from func_portfolio import combine_portfolios

@app.callback(
    [Output('portfolio-allocation', 'value', allow_duplicate=True),
     Output('last-recommendation-button-store', 'data')],
    [Input('use-risk-recommendations', 'n_clicks'),
     Input('use-cvar-recommendations', 'n_clicks')],
    [State('recommended-portfolios-store', 'data'),
     State('recommended-cvar-portfolios-store', 'data'),
     State('portfolio-allocation', 'value')],
    prevent_initial_call=True
)
def load_recommendations(risk_clicks, cvar_clicks, risk_portfolios, cvar_portfolios, current_value):
    ctx = dash.callback_context
    if not ctx.triggered:
        raise dash.exceptions.PreventUpdate
    
    button_id = ctx.triggered[0]['prop_id'].split('.')[0]
    print(f"Recommendation button clicked: {button_id}")
    
    # Function to convert JSON portfolios back to portfolio dictionaries
    def parse_portfolios(portfolios_json):
        try:
            if not portfolios_json:
                print("Empty portfolios JSON")
                return []
                
            portfolios = json.loads(portfolios_json)
            # Filter out None portfolios
            valid_portfolios = [p for p in portfolios if p is not None]
            
            # Ensure all required fields exist in each portfolio
            for p in valid_portfolios:
                p['risk'] = p.get('risk', 0.0001)  # Default if missing
                p['return'] = p.get('return', 0)   # Default if missing
                p['cvar_95'] = p.get('cvar_95', 0) # Default if missing
                
            print(f"Found {len(valid_portfolios)} valid portfolios")
            return valid_portfolios
        except Exception as e:
            print(f"Error parsing portfolios: {e}")
            print(f"Portfolio data: {portfolios_json[:100]}...")  # Print beginning of data for debugging
            return []
    
    # Handle which button was clicked
    if button_id == 'use-risk-recommendations':
        print("Processing risk recommendations")
        portfolios = parse_portfolios(risk_portfolios)
        if not portfolios:
            print("No valid risk portfolios found")
            return current_value, None
        button_type = 'RISK'
    elif button_id == 'use-cvar-recommendations':
        print("Processing CVaR recommendations")
        portfolios = parse_portfolios(cvar_portfolios)
        if not portfolios:
            print("No valid CVaR portfolios found")
            return current_value, None
        button_type = 'CVAR'
    else:
        raise dash.exceptions.PreventUpdate
            
    # Equal weights for all portfolios
    weights = [1.0/len(portfolios)] * len(portfolios)
    print(f"Normalizing weights across {len(portfolios)} portfolios ({weights[0]*100:.2f}% each)")
    
    try:
        # Combine portfolios using the function from gfx_funcs
        combined = combine_portfolios(portfolios, weights)
        
        if not combined or 'combo' not in combined or not combined['combo']:
            print("Failed to create valid combined portfolio")
            return current_value
            
        # Ensure the combined portfolio has the required fields
        combined['risk'] = combined.get('risk', sum(p.get('risk', 0.0001) * w for p, w in zip(portfolios, weights)))
        combined['return'] = combined.get('return', sum(p.get('return', 0) * w for p, w in zip(portfolios, weights)))
        combined['cvar_95'] = combined.get('cvar_95', sum(p.get('cvar_95', 0) * w for p, w in zip(portfolios, weights)))
        
        # Create allocation string
        allocations = []
        for sym, weight in zip(combined['combo'], combined['weights']):
            allocations.append(f"{sym}:{weight:.4f}")
            
        result = ",".join(allocations)
        print(f"Created allocation string with {len(allocations)} symbols")

        # Store both button type and the allocation string for verification
        button_data = {
            'type': button_type,
            'allocation': result
        }
        return result, button_data
    except Exception as e:
        print(f"Error combining portfolios: {e}")
        return current_value, None